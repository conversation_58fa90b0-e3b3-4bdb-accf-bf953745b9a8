/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Poppins', sans-serif;
  background-color: #ffffff;
  color: #000000;
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

input,
select,
textarea,
button {
  outline: 0;
  font-family: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
}

/* Dashboard Container */
.dashboard-container {
  position: relative;
  width: 440px;
  height: 956px;
  margin: 0 auto;
  background: #FFFFFF;
  border: 1px solid #000000;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

/* Status Bar */
.bar-status {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-end;
  padding: 15px 46px;
  gap: 207px;
  position: absolute;
  width: 440px;
  height: 56px;
  left: 0px;
  top: 0px;
  background: #FFFFFF;
}

.clock {
  width: 54px;
  height: 20px;
  flex: none;
  order: 0;
  flex-grow: 0;
}

.clock-text {
  position: absolute;
  width: 54px;
  height: 20px;
  left: 46px;
  top: 21px;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 17px;
  line-height: 22px;
  text-align: center;
  letter-spacing: -0.408px;
  font-feature-settings: 'case' on;
  color: #000000;
}

.right-side {
  width: 77.4px;
  height: 13px;
  flex: none;
  order: 1;
  flex-grow: 0;
  position: relative;
}

.mobile-signal {
  position: absolute;
  width: 18px;
  height: 12px;
  left: calc(50% - 18px/2 + 105.6px);
  top: 29px;
  background: #000000;
}

.wifi {
  position: absolute;
  left: 77.86%;
  right: 18.27%;
  top: 51.79%;
  bottom: 27.08%;
  background: #000000;
}

.wifi-path-1 {
  position: absolute;
  width: 17px;
  height: 5.39px;
  left: calc(50% - 17px/2 + 131.1px);
  top: 29px;
  background: linear-gradient(0deg, #B5CDFF, #B5CDFF), #D9D9D9;
}

.wifi-path-2 {
  position: absolute;
  width: 11.07px;
  height: 4.13px;
  left: calc(50% - 11.07px/2 + 131.14px);
  top: 33px;
  background: linear-gradient(0deg, #B5CDFF, #B5CDFF), #D9D9D9;
}

.wifi-path-3 {
  position: absolute;
  width: 5.15px;
  height: 3.83px;
  left: calc(50% - 5.15px/2 + 131.17px);
  top: 37px;
  background: linear-gradient(0deg, #B5CDFF, #B5CDFF), #D9D9D9;
}

.status-bar-battery {
  position: absolute;
  width: 27.4px;
  height: 13px;
  left: calc(50% - 27.4px/2 + 160.3px);
  top: 28px;
}

.battery-outline {
  box-sizing: border-box;
  position: absolute;
  width: 25px;
  height: 13px;
  left: calc(50% - 25px/2 - 1.2px);
  top: calc(50% - 13px/2);
  mix-blend-mode: normal;
  opacity: 0.35;
  border: 1px solid #000000;
  border-radius: 4px;
}

.battery-end {
  position: absolute;
  width: 1.4px;
  height: 4.22px;
  left: calc(50% - 1.4px/2 + 13px);
  top: calc(50% - 4.22px/2 + 0.61px);
  background: #000000;
  mix-blend-mode: normal;
  opacity: 0.4;
}

.battery-fill {
  position: absolute;
  width: 21px;
  height: 9px;
  left: calc(50% - 21px/2 - 1.2px);
  top: calc(50% - 9px/2);
  background: #000000;
  border-radius: 2px;
}

/* Background Image */
.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 440px;
  height: 956px;
  background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDQwIiBoZWlnaHQ9Ijk1NiIgdmlld0JveD0iMCAwIDQ0MCA5NTYiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI0NDAiIGhlaWdodD0iOTU2IiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=') no-repeat center center;
  background-size: cover;
  z-index: -1;
}

/* Header Section */
.header-section {
  position: absolute;
  top: 80px;
  left: 0;
  right: 0;
  padding: 20px 40px;
  background: linear-gradient(135deg, #67C16D 0%, #4A9C50 100%);
  margin: 0 20px;
  border-radius: 20px;
}

.greeting-container {
  margin-bottom: 20px;
}

.greeting-text {
  display: flex;
  align-items: center;
  gap: 8px;
}

.greeting {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #979797;
}

.sun-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.user-info-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.user-info {
  flex: 1;
  max-width: 280px;
}

.user-name {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 36px;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.user-desc {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: #FFFFFF;
  margin: 0;
}

.profile-picture {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.profile-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

/* Main Content */
.main-content {
  position: absolute;
  top: 240px;
  left: 20px;
  right: 20px;
  bottom: 150px;
  overflow-y: auto;
}

/* Balance Card */
.balance-card {
  position: relative;
  width: 100%;
  height: 180px;
  margin-bottom: 20px;
  border-radius: 14px;
  overflow: hidden;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #67C16D 0%, #4A9C50 100%);
  border-radius: 14px;
}

.card-content {
  position: relative;
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  z-index: 1;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-title {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 18px;
  line-height: 27px;
  color: #FFFFFF;
}

.card-number {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #FFFFFF;
  opacity: 0.8;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.balance-label {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 14px;
  line-height: 21px;
  color: #FFFFFF;
  opacity: 0.8;
}

.balance-amount {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 700;
  font-size: 28px;
  line-height: 42px;
  color: #FFFFFF;
}

.last-transaction {
  align-self: flex-end;
}

.last-txn {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #FFFFFF;
  opacity: 0.8;
}

/* Stats and Action Section */
.stats-action-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Monthly Stats Card */
.monthly-stats-card {
  background: #FFFFFF;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.stats-info {
  flex: 1;
}

.stats-title {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 14px;
  line-height: 21px;
  color: #0D2610;
  margin: 0 0 8px 0;
}

.stats-value {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 36px;
  color: #0D2610;
  margin-bottom: 12px;
}

.stats-detail {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stats-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stats-text {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 400;
  font-size: 12px;
  line-height: 18px;
  color: #0D2610;
}

.action-button {
  margin-left: 16px;
}

.setor-button {
  background: #67C16D;
  border-radius: 10px;
  padding: 12px 24px;
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.setor-button:hover {
  background: #5BA862;
  transform: translateY(-1px);
  box-shadow: 0px 6px 20px rgba(103, 193, 109, 0.3);
}

/* Promo Cards */
.promo-cards {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.promo-card {
  position: relative;
  height: 160px;
  border-radius: 40px;
  overflow: hidden;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
}

.promo-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #67C16D;
  border-radius: 40px;
}

.promo-content {
  position: relative;
  padding: 27px 20px;
  height: 100%;
  display: flex;
  align-items: flex-start;
  gap: 16px;
  z-index: 1;
}

.promo-icon {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.promo-icon-placeholder {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.promo-text {
  flex: 1;
  padding-top: 20px;
}

.promo-title {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 600;
  font-size: 24px;
  line-height: 36px;
  color: #FFFFFF;
  margin: 0 0 8px 0;
}

.promo-desc {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 16px;
  line-height: 24px;
  color: #FFFFFF;
  margin: 0;
  opacity: 0.9;
}

/* Bottom Navigation */
.bottom-navbar {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 126px;
  background: #FFFFFF;
  box-shadow: 0px -5px 5px rgba(0, 0, 0, 0.1);
}

.navbar-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #FFFFFF;
}

.navbar-content {
  position: relative;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 20px 16px;
  height: 100%;
  z-index: 1;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.nav-item.active .nav-icon {
  color: #67C16D;
}

.nav-item.active .nav-label {
  color: #67C16D;
}

.nav-icon {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #979797;
  transition: color 0.2s ease;
}

.nav-item:hover .nav-icon {
  color: #67C16D;
}

.nav-item:hover .nav-label {
  color: #67C16D;
}

.nav-label {
  font-family: 'Poppins';
  font-style: normal;
  font-weight: 500;
  font-size: 12px;
  line-height: 18px;
  color: #979797;
  transition: color 0.2s ease;
}

/* Center Navigation Item (Scan) */
.nav-item.nav-center {
  position: relative;
}

.nav-icon-center {
  position: relative;
  width: 63px;
  height: 63px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.center-bg {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #67C16D;
  border-radius: 50%;
  z-index: 0;
}

.nav-icon-center svg {
  position: relative;
  z-index: 1;
}

.nav-center .nav-label {
  color: #67C16D;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 440px) {
  .dashboard-container {
    width: 100vw;
    height: 100vh;
    border: none;
    border-radius: 0;
  }

  .bar-status {
    width: 100%;
  }

  .header-section {
    left: 16px;
    right: 16px;
  }

  .main-content {
    left: 16px;
    right: 16px;
  }
}

/* Additional hover effects */
.balance-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.monthly-stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 30px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
}

.promo-card:hover {
  transform: translateY(-2px);
  box-shadow: 0px 8px 30px rgba(103, 193, 109, 0.3);
  transition: all 0.3s ease;
}