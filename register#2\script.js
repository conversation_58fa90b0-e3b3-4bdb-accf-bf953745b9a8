// SakuHijau Registration Form JavaScript

// DOM elements
const registrationForm = document.getElementById('registrationForm');
const firstNameInput = document.getElementById('firstName');
const lastNameInput = document.getElementById('lastName');
const phoneNumberInput = document.getElementById('phoneNumber');
const submitButton = document.querySelector('.submit-button');
const countrySelector = document.querySelector('.country-selector');

// Form validation functions
function validateName(name) {
    return name.trim().length >= 2 && /^[a-zA-Z\s]+$/.test(name.trim());
}

function validatePhoneNumber(phone) {
    // Indonesian phone number validation (8-13 digits)
    const cleanPhone = phone.replace(/\D/g, '');
    return cleanPhone.length >= 8 && cleanPhone.length <= 13;
}

// Show error message
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.classList.add('error');
    errorElement.textContent = message;
    formGroup.classList.add('has-error');
}

// Clear error message
function clearError(input) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.classList.remove('error');
    formGroup.classList.remove('has-error');
    if (errorElement) {
        errorElement.textContent = '';
    }
}

// Format phone number as user types
function formatPhoneNumber(value) {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Format based on length
    if (digits.length <= 3) {
        return digits;
    } else if (digits.length <= 7) {
        return digits.slice(0, 3) + '-' + digits.slice(3);
    } else if (digits.length <= 11) {
        return digits.slice(0, 3) + '-' + digits.slice(3, 7) + '-' + digits.slice(7);
    } else {
        return digits.slice(0, 3) + '-' + digits.slice(3, 7) + '-' + digits.slice(7, 11);
    }
}

// Real-time validation
function setupRealTimeValidation() {
    firstNameInput.addEventListener('blur', function() {
        const firstName = this.value.trim();
        if (firstName && !validateName(firstName)) {
            showError(this, 'First name must be at least 2 characters and contain only letters');
        } else {
            clearError(this);
        }
    });

    lastNameInput.addEventListener('blur', function() {
        const lastName = this.value.trim();
        if (lastName && !validateName(lastName)) {
            showError(this, 'Last name must be at least 2 characters and contain only letters');
        } else {
            clearError(this);
        }
    });

    phoneNumberInput.addEventListener('input', function() {
        // Format the phone number as user types
        const formatted = formatPhoneNumber(this.value);
        this.value = formatted;
    });

    phoneNumberInput.addEventListener('blur', function() {
        const phone = this.value.replace(/\D/g, '');
        if (phone && !validatePhoneNumber(phone)) {
            showError(this, 'Please enter a valid Indonesian phone number (8-13 digits)');
        } else {
            clearError(this);
        }
    });
}

// Country selector functionality
function setupCountrySelector() {
    countrySelector.addEventListener('click', function() {
        // In a real app, this would open a country selection modal
        alert('Country selection feature would be implemented here.\nCurrently set to Indonesia (+62)');
    });
}

// Form submission
function handleFormSubmission(event) {
    event.preventDefault();
    
    // Clear all previous errors
    [firstNameInput, lastNameInput, phoneNumberInput].forEach(clearError);
    
    let isValid = true;
    
    // Validate first name
    const firstName = firstNameInput.value.trim();
    if (!firstName) {
        showError(firstNameInput, 'First name is required');
        isValid = false;
    } else if (!validateName(firstName)) {
        showError(firstNameInput, 'First name must be at least 2 characters and contain only letters');
        isValid = false;
    }
    
    // Validate last name
    const lastName = lastNameInput.value.trim();
    if (!lastName) {
        showError(lastNameInput, 'Last name is required');
        isValid = false;
    } else if (!validateName(lastName)) {
        showError(lastNameInput, 'Last name must be at least 2 characters and contain only letters');
        isValid = false;
    }
    
    // Validate phone number
    const phone = phoneNumberInput.value.replace(/\D/g, '');
    if (!phone) {
        showError(phoneNumberInput, 'Phone number is required');
        isValid = false;
    } else if (!validatePhoneNumber(phone)) {
        showError(phoneNumberInput, 'Please enter a valid Indonesian phone number (8-13 digits)');
        isValid = false;
    }
    
    if (isValid) {
        // Show loading state
        submitButton.classList.add('loading');
        submitButton.disabled = true;
        
        // Simulate form submission
        setTimeout(() => {
            alert(`Registration data:\nFirst Name: ${firstName}\nLast Name: ${lastName}\nPhone: +62${phone}\n\nProceeding to next step...`);
            
            // Reset loading state
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
            
            // In a real app, you would navigate to the next step
            console.log('Form submitted successfully:', {
                firstName,
                lastName,
                phone: '+62' + phone
            });
        }, 2000);
    }
}

// Input focus management for better UX
function setupInputFocusManagement() {
    // Auto-focus next input on Enter key
    firstNameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            lastNameInput.focus();
        }
    });
    
    lastNameInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            phoneNumberInput.focus();
        }
    });
    
    phoneNumberInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            submitButton.click();
        }
    });
}

// Initialize the application
function initializeApp() {
    // Set up event listeners
    registrationForm.addEventListener('submit', handleFormSubmission);
    setupRealTimeValidation();
    setupCountrySelector();
    setupInputFocusManagement();
    
    // Focus on first input for better UX
    firstNameInput.focus();
    
    console.log('SakuHijau Registration Form (Step 2) initialized');
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateName,
        validatePhoneNumber,
        formatPhoneNumber
    };
}
