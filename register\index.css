/* CSS Variables for consistent theming */
:root {
  --primary-green: #67c16d;
  --light-green: #c7ebc9;
  --background-green: #f3faf3;
  --dark-text: #0d2610;
  --placeholder-text: #979797;
  --border-color: #e0e0e0;
  --white: #ffffff;
  --black: #000000;

  --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, sans-serif;

  --border-radius: 8px;
  --shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);

  --mobile-max-width: 440px;
  --desktop-max-width: 500px;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--white);
  color: var(--dark-text);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

input,
select,
textarea,
button {
  outline: 0;
  font-family: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
}

/* Main container */
.app-container {
  min-height: 100vh;
  max-width: var(--mobile-max-width);
  margin: 0 auto;
  background: var(--white);
  position: relative;
  overflow-x: hidden;
}
/* Status bar (mobile only) */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: var(--white);
  height: 56px;
}

.mobile-only {
  display: none;
}

.status-time {
  font-size: 17px;
  font-weight: 600;
  color: var(--black);
  letter-spacing: -0.41px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 18px;
  height: 12px;
  background-color: var(--black);
  border-radius: 2px;
}

.battery-icon {
  width: 24px;
  height: 12px;
  border: 1px solid var(--black);
  background: linear-gradient(to right, var(--black) 70%, transparent 70%);
}

.wifi-icon {
  width: 16px;
  height: 12px;
  background: radial-gradient(circle at bottom, var(--black) 30%, transparent 30%);
}
/* Main content area */
.main-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  min-height: calc(100vh - 200px);
}

/* App header */
.app-header {
  text-align: center;
  margin-bottom: 8px;
}

.app-title {
  color: var(--primary-green);
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}

/* Tab navigation */
.tab-container {
  display: flex;
  background: var(--background-green);
  border-radius: var(--border-radius);
  padding: 4px;
  width: 100%;
  max-width: 342px;
}

.tab-button {
  flex: 1;
  padding: 8px 16px;
  font-size: 18px;
  font-weight: 400;
  color: var(--dark-text);
  background: transparent;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: center;
  min-height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-button.active {
  background: var(--light-green);
}

.tab-button:hover:not(.active) {
  background: rgba(199, 235, 201, 0.5);
}
/* Registration form */
.registration-form {
  width: 100%;
  max-width: 342px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-label {
  font-size: 18px;
  font-weight: 400;
  color: var(--dark-text);
  line-height: 1.5;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  font-size: 14px;
  color: var(--dark-text);
  background: var(--white);
  border: none;
  border-top: 1px solid var(--border-color);
  border-radius: 0;
  transition: border-color 0.2s ease;
}

.form-input:focus {
  border-top-color: var(--primary-green);
  outline: none;
}

.form-input::placeholder {
  color: var(--placeholder-text);
}

.form-input:invalid:not(:placeholder-shown) {
  border-top-color: #e74c3c;
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-icon {
  font-size: 16px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.password-toggle:hover .password-icon {
  opacity: 1;
}
/* Checkbox group */
.checkbox-group {
  margin-top: 8px;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  user-select: none;
}

.checkbox-container input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 3px;
  position: relative;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
  background-color: var(--primary-green);
  border-color: var(--primary-green);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.checkbox-text {
  font-size: 12px;
  color: var(--dark-text);
  line-height: 1.5;
}
/* Submit button */
.submit-button {
  width: 100%;
  padding: 12px 24px;
  background: var(--light-green);
  color: var(--dark-text);
  font-size: 18px;
  font-weight: 400;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button:hover {
  background: var(--primary-green);
  color: var(--white);
  transform: translateY(-1px);
}

.submit-button:active {
  transform: translateY(0);
}

.submit-button:disabled {
  background: var(--border-color);
  color: var(--placeholder-text);
  cursor: not-allowed;
  transform: none;
}
/* Progress indicator */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
}

.progress-dot {
  width: 24px;
  height: 13px;
  background: var(--light-green);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.progress-dot.active {
  width: 34px;
  background: var(--primary-green);
}

/* Bottom illustration */
.bottom-illustration {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: var(--mobile-max-width);
  height: 200px;
  pointer-events: none;
  z-index: -1;
}

.earth-hands {
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><linearGradient id="earth" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2367c16d;stop-opacity:1" /><stop offset="100%" style="stop-color:%234a9c50;stop-opacity:1" /></linearGradient></defs><circle cx="200" cy="150" r="80" fill="url(%23earth)"/><path d="M120 150 Q200 100 280 150" stroke="%2367c16d" stroke-width="3" fill="none"/><path d="M140 170 Q200 120 260 170" stroke="%2367c16d" stroke-width="2" fill="none"/></svg>') no-repeat center bottom;
  background-size: contain;
  opacity: 0.8;
}

/* Responsive design */
@media (min-width: 768px) {
  .app-container {
    max-width: var(--desktop-max-width);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px auto;
    min-height: calc(100vh - 40px);
  }

  .main-content {
    padding: 40px;
  }

  .app-title {
    font-size: 36px;
  }

  .mobile-only {
    display: none !important;
  }
}

@media (max-width: 767px) {
  .mobile-only {
    display: flex;
  }

  .app-container {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .main-content {
    padding: 16px;
  }
}

/* Focus and accessibility improvements */
.form-input:focus,
.tab-button:focus,
.submit-button:focus,
.checkbox-container:focus-within {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* Animation for smooth transitions */
* {
  transition: border-color 0.2s ease, background-color 0.2s ease, color 0.2s ease;
}

/* Error states */
.form-input.error {
  border-top-color: #e74c3c;
}

.error-message {
  color: #e74c3c;
  font-size: 12px;
  margin-top: 4px;
  display: none;
}

.form-group.has-error .error-message {
  display: block;
}
