<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SakuHijau - Verifi<PERSON><PERSON></title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@600&display=swap" />
    <link rel="stylesheet" href="index.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Mobile status bar (only visible on mobile) -->
      <div class="status-bar mobile-only">
        <div class="status-time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>

      <!-- Main content -->
      <div class="main-content">
        <header class="app-header">
          <h1 class="app-title">SakuHijau</h1>
        </header>

        <!-- Verification form -->
        <form class="verification-form" id="verificationForm" novalidate>
          <div class="verification-section">
            <!-- Email icon -->
            <div class="email-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z" stroke="currentColor" stroke-width="2" fill="none"/>
                <polyline points="22,6 12,13 2,6" stroke="currentColor" stroke-width="2"/>
              </svg>
            </div>

            <div class="verification-info">
              <h2 class="verification-title">Verify Your Email</h2>
              <p class="verification-subtitle">
                We've sent a 4-digit verification code to<br>
                <span class="email-address" id="emailAddress">your email address</span>
              </p>
            </div>

            <!-- OTP Input -->
            <div class="otp-container">
              <input type="text" class="otp-input" id="otp1" maxlength="1" autocomplete="off" />
              <input type="text" class="otp-input" id="otp2" maxlength="1" autocomplete="off" />
              <input type="text" class="otp-input" id="otp3" maxlength="1" autocomplete="off" />
              <input type="text" class="otp-input" id="otp4" maxlength="1" autocomplete="off" />
            </div>

            <!-- Timer and Resend -->
            <div class="resend-section">
              <div class="timer-container" id="timerContainer">
                <span class="timer-text">Resend code in </span>
                <span class="timer-countdown" id="timerCountdown">60</span>
                <span class="timer-text">s</span>
              </div>

              <div class="resend-container" id="resendContainer" style="display: none;">
                <span class="resend-text">Didn't get the code? </span>
                <button type="button" class="resend-button" id="resendBtn">Resend it</button>
              </div>
            </div>

            <!-- Error message -->
            <div class="error-message" id="errorMessage" style="display: none;"></div>

            <!-- Success message -->
            <div class="success-message" id="successMessage" style="display: none;">
              Code verified successfully!
            </div>
          </div>

          <button type="submit" class="submit-button" id="verifyBtn" disabled>
            Verify Code
          </button>
        </form>

        <!-- Progress indicator -->
        <div class="progress-indicator">
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot active"></div>
        </div>
      </div>

      <!-- Bottom illustration -->
      <div class="bottom-illustration">
        <div class="earth-hands"></div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
