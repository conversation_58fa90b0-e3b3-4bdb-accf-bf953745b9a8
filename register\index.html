<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SakuHijau - Registrasi</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" />
    <link rel="stylesheet" href="index.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Mobile status bar (only visible on mobile) -->
      <div class="status-bar mobile-only">
        <div class="status-time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>

      <!-- Main content -->
      <div class="main-content">
        <header class="app-header">
          <h1 class="app-title">SakuHijau</h1>
        </header>

        <!-- Tab navigation -->
        <div class="tab-container">
          <button class="tab-button" id="login-tab" onclick="switchTab('login')">Masuk</button>
          <button class="tab-button active" id="register-tab" onclick="switchTab('register')">Registrasi</button>
        </div>

        <!-- Registration form -->
        <form class="registration-form" id="registrationForm" novalidate>
          <div class="form-group">
            <label for="email" class="form-label">Email</label>
            <div class="input-container">
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                placeholder="Your email"
                required
              />
            </div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">Password</label>
            <div class="input-container">
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                placeholder="Password"
                required
                minlength="6"
              />
              <button type="button" class="password-toggle" onclick="togglePassword('password')">
                <span class="password-icon" id="password-icon">👁️</span>
              </button>
            </div>
          </div>

          <div class="form-group">
            <label for="confirmPassword" class="form-label">Confirm Password</label>
            <div class="input-container">
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                class="form-input"
                placeholder="Password"
                required
                minlength="6"
              />
              <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                <span class="password-icon" id="confirmPassword-icon">👁️</span>
              </button>
            </div>
          </div>

          <div class="form-group checkbox-group">
            <label class="checkbox-container">
              <input type="checkbox" id="terms" name="terms" required />
              <span class="checkmark"></span>
              <span class="checkbox-text">I accept the terms and policies</span>
            </label>
          </div>

          <button type="submit" class="submit-button">Register</button>
        </form>

        <!-- Progress indicator -->
        <div class="progress-indicator">
          <div class="progress-dot active"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
        </div>
      </div>

      <!-- Bottom illustration -->
      <div class="bottom-illustration">
        <div class="earth-hands"></div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
