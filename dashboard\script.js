// SakuHijau Dashboard JavaScript

// DOM elements
const navItems = document.querySelectorAll('.nav-item');
const setorButton = document.querySelector('.setor-button');
const balanceCard = document.querySelector('.balance-card');
const promoCards = document.querySelectorAll('.promo-card');

// Navigation functionality
function handleNavigation(targetPage, clickedElement) {
    // Remove active class from all nav items
    navItems.forEach(item => item.classList.remove('active'));

    // Add active class to clicked item
    if (clickedElement) {
        clickedElement.classList.add('active');
    }

    // Handle navigation based on target page
    switch(targetPage) {
        case 'home':
            console.log('Navigating to Home');
            // Already on home page
            break;
        case 'waste':
            console.log('Navigating to Waste Management');
            alert('Waste Management page - Feature coming soon!');
            break;
        case 'scan':
            console.log('Opening Scanner');
            alert('QR/Barcode Scanner - Feature coming soon!');
            break;
        case 'history':
            console.log('Navigating to History');
            alert('Transaction History - Feature coming soon!');
            break;
        case 'profile':
            console.log('Navigating to Profile');
            alert('User Profile - Feature coming soon!');
            break;
        default:
            console.log('Unknown navigation target');
    }
}

// Setor (Deposit) functionality
function handleSetor() {
    console.log('Setor button clicked');

    // Show loading state
    setorButton.textContent = 'Processing...';
    setorButton.disabled = true;
    setorButton.style.background = '#5BA862';

    // Simulate processing
    setTimeout(() => {
        alert('Setor Sampah\n\nFitur ini akan mengarahkan Anda ke halaman setor sampah untuk mencatat setoran sampah baru.');

        // Reset button state
        setorButton.textContent = 'Setor';
        setorButton.disabled = false;
        setorButton.style.background = '#67C16D';
    }, 1500);
}

// Balance card interaction
function handleBalanceCardClick() {
    console.log('Balance card clicked');
    alert('Detail Saldo\n\nSaldo: Rp.5,500\nTransaksi Terakhir: 12/03/2025\nTotal Setoran Bulan Ini: 100,00 Kg');
}

// Promo card interactions
function handlePromoCardClick(promoIndex) {
    console.log(`Promo card ${promoIndex + 1} clicked`);

    const promoTitles = [
        'Ayo Jaga Kebersihan',
        'Program Lingkungan Hijau'
    ];

    const promoDescriptions = [
        'Bergabunglah dalam program kebersihan lingkungan untuk masa depan yang lebih hijau.',
        'Ikuti program lingkungan hijau dan dapatkan reward menarik!'
    ];

    alert(`${promoTitles[promoIndex] || 'Promo'}\n\n${promoDescriptions[promoIndex] || 'Jaga kebersihan seperti lorem ipsum'}`);
}

// Greeting time update
function updateGreeting() {
    const greetingElement = document.querySelector('.greeting');
    const now = new Date();
    const hour = now.getHours();

    let greeting;
    if (hour < 12) {
        greeting = 'Selamat Pagi';
    } else if (hour < 15) {
        greeting = 'Selamat Siang';
    } else if (hour < 18) {
        greeting = 'Selamat Sore';
    } else {
        greeting = 'Selamat Malam';
    }

    if (greetingElement) {
        greetingElement.textContent = greeting;
    }
}

// Clock update
function updateClock() {
    const clockElement = document.querySelector('.clock-text');
    const now = new Date();
    const timeString = now.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
    });

    if (clockElement) {
        clockElement.textContent = timeString;
    }
}

// Smooth scroll for main content
function initSmoothScroll() {
    const mainContent = document.querySelector('.main-content');
    if (mainContent) {
        mainContent.style.scrollBehavior = 'smooth';
    }
}

// Add ripple effect to buttons
function addRippleEffect(element, event) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Initialize event listeners
function initializeEventListeners() {
    // Navigation items
    navItems.forEach((item, index) => {
        item.addEventListener('click', (event) => {
            const navTargets = ['home', 'waste', 'scan', 'history', 'profile'];
            handleNavigation(navTargets[index], event.currentTarget);
        });
    });

    // Setor button
    if (setorButton) {
        setorButton.addEventListener('click', handleSetor);
    }

    // Balance card
    if (balanceCard) {
        balanceCard.addEventListener('click', handleBalanceCardClick);
    }

    // Promo cards
    promoCards.forEach((card, index) => {
        card.addEventListener('click', () => handlePromoCardClick(index));
    });

    // Add ripple effect to interactive elements
    const interactiveElements = document.querySelectorAll('.setor-button, .nav-item, .balance-card, .promo-card');
    interactiveElements.forEach(element => {
        element.addEventListener('click', (event) => {
            addRippleEffect(element, event);
        });
    });
}

// Initialize the dashboard
function initializeDashboard() {
    console.log('SakuHijau Dashboard initialized');

    // Update greeting and clock
    updateGreeting();
    updateClock();

    // Update clock every minute
    setInterval(updateClock, 60000);

    // Initialize smooth scrolling
    initSmoothScroll();

    // Initialize event listeners
    initializeEventListeners();

    // Add CSS for ripple effect
    const style = document.createElement('style');
    style.textContent = `
        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        .nav-item, .setor-button, .balance-card, .promo-card {
            position: relative;
            overflow: hidden;
        }
    `;
    document.head.appendChild(style);
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
} else {
    initializeDashboard();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        handleNavigation,
        handleSetor,
        handleBalanceCardClick,
        handlePromoCardClick,
        updateGreeting,
        updateClock
    };
}