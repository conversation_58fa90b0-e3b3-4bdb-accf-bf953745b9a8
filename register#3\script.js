// SakuHijau Profile Picture Upload JavaScript

// DOM elements
const uploadForm = document.getElementById('uploadForm');
const fileInput = document.getElementById('fileInput');
const cameraInput = document.getElementById('cameraInput');
const chooseFileBtn = document.getElementById('chooseFileBtn');
const takePictureBtn = document.getElementById('takePictureBtn');
const skipBtn = document.getElementById('skipBtn');
const profilePreview = document.getElementById('profilePreview');
const previewImage = document.getElementById('previewImage');
const uploadProgress = document.getElementById('uploadProgress');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const errorMessage = document.getElementById('errorMessage');
const continueBtn = document.getElementById('continueBtn');

// State management
let selectedFile = null;
let isUploading = false;

// File validation
function validateFile(file) {
    const maxSize = 5 * 1024 * 1024; // 5MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    
    if (!allowedTypes.includes(file.type)) {
        return 'Please select a valid image file (JPEG, PNG, or WebP)';
    }
    
    if (file.size > maxSize) {
        return 'File size must be less than 5MB';
    }
    
    return null;
}

// Show error message
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    setTimeout(() => {
        errorMessage.style.display = 'none';
    }, 5000);
}

// Clear error message
function clearError() {
    errorMessage.style.display = 'none';
}

// Preview image
function previewFile(file) {
    const reader = new FileReader();
    
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        previewImage.style.display = 'block';
        profilePreview.classList.add('has-image');
        
        // Hide placeholder icon
        const placeholderIcon = profilePreview.querySelector('.placeholder-icon');
        if (placeholderIcon) {
            placeholderIcon.style.display = 'none';
        }
        
        // Show continue button
        continueBtn.style.display = 'flex';
    };
    
    reader.readAsDataURL(file);
}

// Simulate file upload with progress
function simulateUpload(file) {
    return new Promise((resolve, reject) => {
        isUploading = true;
        uploadProgress.style.display = 'block';
        
        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;
            
            progressFill.style.width = progress + '%';
            progressText.textContent = `Uploading... ${Math.round(progress)}%`;
            
            if (progress >= 100) {
                clearInterval(interval);
                setTimeout(() => {
                    uploadProgress.style.display = 'none';
                    isUploading = false;
                    resolve(file);
                }, 500);
            }
        }, 100);
    });
}

// Handle file selection
function handleFileSelect(file) {
    clearError();
    
    const error = validateFile(file);
    if (error) {
        showError(error);
        return;
    }
    
    selectedFile = file;
    previewFile(file);
    
    // Simulate upload
    simulateUpload(file).then(() => {
        console.log('File uploaded successfully:', file.name);
    }).catch((error) => {
        showError('Upload failed. Please try again.');
        console.error('Upload error:', error);
    });
}

// Event listeners
chooseFileBtn.addEventListener('click', function() {
    if (isUploading) return;
    fileInput.click();
});

takePictureBtn.addEventListener('click', function() {
    if (isUploading) return;
    
    // Check if camera is available
    if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        cameraInput.click();
    } else {
        showError('Camera access is not available on this device');
    }
});

skipBtn.addEventListener('click', function() {
    if (isUploading) return;
    
    // In a real app, this would proceed to the next step without a profile picture
    alert('Skipping profile picture upload.\nYou can add one later in your profile settings.');
    console.log('User skipped profile picture upload');
});

fileInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        handleFileSelect(file);
    }
});

cameraInput.addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        handleFileSelect(file);
    }
});

// Drag and drop functionality
profilePreview.addEventListener('dragover', function(e) {
    e.preventDefault();
    if (!isUploading) {
        profilePreview.classList.add('drag-over');
    }
});

profilePreview.addEventListener('dragleave', function(e) {
    e.preventDefault();
    profilePreview.classList.remove('drag-over');
});

profilePreview.addEventListener('drop', function(e) {
    e.preventDefault();
    profilePreview.classList.remove('drag-over');
    
    if (isUploading) return;
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        handleFileSelect(files[0]);
    }
});

// Click on preview to change image
profilePreview.addEventListener('click', function() {
    if (!isUploading) {
        fileInput.click();
    }
});

// Form submission
uploadForm.addEventListener('submit', function(e) {
    e.preventDefault();
    
    if (!selectedFile) {
        showError('Please select a profile picture or skip this step');
        return;
    }
    
    // In a real app, this would proceed to the next registration step
    alert(`Profile picture selected: ${selectedFile.name}\nProceeding to next step...`);
    console.log('Form submitted with file:', selectedFile);
});

// Keyboard navigation
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && isUploading) {
        // In a real app, this could cancel the upload
        console.log('Upload cancellation requested');
    }
});

// Initialize the application
function initializeApp() {
    console.log('SakuHijau Profile Picture Upload initialized');
    
    // Check for drag and drop support
    if ('draggable' in document.createElement('div')) {
        console.log('Drag and drop supported');
    }
    
    // Check for file API support
    if (window.File && window.FileReader && window.FileList && window.Blob) {
        console.log('File API supported');
    } else {
        showError('File upload is not supported in this browser');
    }
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateFile,
        handleFileSelect
    };
}
