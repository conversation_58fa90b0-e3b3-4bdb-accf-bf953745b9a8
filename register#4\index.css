/* CSS Variables for consistent theming */
:root {
  --primary-green: #67c16d;
  --light-green: #c7ebc9;
  --background-green: #f3faf3;
  --dark-text: #0d2610;
  --placeholder-text: #979797;
  --border-color: #e0e0e0;
  --white: #ffffff;
  --black: #000000;
  --error-color: #e74c3c;
  --success-color: #27ae60;
  --timer-color: #f39c12;

  --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, sans-serif;
  --font-family-alt: 'Plus Jakarta Sans', var(--font-family);

  --border-radius: 8px;
  --border-radius-large: 16px;
  --shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  --shadow-hover: 0 6px 12px 0 rgba(0, 0, 0, 0.15);

  --mobile-max-width: 440px;
  --desktop-max-width: 500px;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--white);
  color: var(--dark-text);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

input,
select,
textarea,
button {
  outline: 0;
  font-family: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
}

/* Main container */
.app-container {
  min-height: 100vh;
  max-width: var(--mobile-max-width);
  margin: 0 auto;
  background: var(--white);
  position: relative;
  overflow-x: hidden;
}
/* Status bar (mobile only) */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: var(--white);
  height: 56px;
}

.mobile-only {
  display: none;
}

.status-time {
  font-size: 17px;
  font-weight: 600;
  color: var(--black);
  letter-spacing: -0.41px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 18px;
  height: 12px;
  background-color: var(--black);
  border-radius: 2px;
}

.battery-icon {
  width: 24px;
  height: 12px;
  border: 1px solid var(--black);
  background: linear-gradient(to right, var(--black) 70%, transparent 70%);
}

.wifi-icon {
  width: 16px;
  height: 12px;
  background: radial-gradient(circle at bottom, var(--black) 30%, transparent 30%);
}

/* Main content area */
.main-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  min-height: calc(100vh - 200px);
}

/* App header */
.app-header {
  text-align: center;
  margin-bottom: 16px;
}

.app-title {
  color: var(--primary-green);
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}
/* Verification form */
.verification-form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.verification-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  text-align: center;
}

/* Email icon */
.email-icon {
  width: 80px;
  height: 80px;
  color: var(--primary-green);
  background: var(--background-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
}

/* Verification info */
.verification-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 320px;
}

.verification-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--dark-text);
  margin: 0;
}

.verification-subtitle {
  font-size: 14px;
  color: var(--placeholder-text);
  line-height: 1.5;
  margin: 0;
}

.email-address {
  color: var(--primary-green);
  font-weight: 600;
}
/* OTP Input Container */
.otp-container {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin: 16px 0;
}

.otp-input {
  width: 60px;
  height: 60px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  color: var(--dark-text);
  background: var(--white);
  transition: all 0.2s ease;
}

.otp-input:focus {
  border-color: var(--primary-green);
  outline: none;
  box-shadow: 0 0 0 3px rgba(103, 193, 109, 0.1);
}

.otp-input.filled {
  border-color: var(--primary-green);
  background: var(--background-green);
}

.otp-input.error {
  border-color: var(--error-color);
  background: rgba(231, 76, 60, 0.1);
}
/* Timer and Resend Section */
.resend-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
}

.timer-container {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  color: var(--placeholder-text);
}

.timer-countdown {
  color: var(--timer-color);
  font-weight: 600;
  min-width: 20px;
}

.resend-container {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.resend-text {
  color: var(--placeholder-text);
}

.resend-button {
  color: var(--primary-green);
  font-weight: 600;
  text-decoration: underline;
  cursor: pointer;
  transition: color 0.2s ease;
}

.resend-button:hover {
  color: var(--dark-text);
}

.resend-button:disabled {
  color: var(--placeholder-text);
  cursor: not-allowed;
  text-decoration: none;
}

/* Submit button */
.submit-button {
  width: 100%;
  max-width: 343px;
  padding: 12px 24px;
  background: var(--light-green);
  color: var(--dark-text);
  font-size: 18px;
  font-weight: 400;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button:hover:not(:disabled) {
  background: var(--primary-green);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.submit-button:disabled {
  background: var(--border-color);
  color: var(--placeholder-text);
  cursor: not-allowed;
  transform: none;
}
/* Error and Success Messages */
.error-message {
  color: var(--error-color);
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: rgba(231, 76, 60, 0.1);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 343px;
  margin-top: 8px;
}

.success-message {
  color: var(--success-color);
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: rgba(39, 174, 96, 0.1);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 343px;
  margin-top: 8px;
}

/* Progress indicator */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
}

.progress-dot {
  width: 24px;
  height: 13px;
  background: var(--light-green);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.progress-dot.active {
  width: 34px;
  background: var(--primary-green);
}

/* Bottom illustration */
.bottom-illustration {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: var(--mobile-max-width);
  height: 200px;
  pointer-events: none;
  z-index: -1;
}

.earth-hands {
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><linearGradient id="earth" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2367c16d;stop-opacity:1" /><stop offset="100%" style="stop-color:%234a9c50;stop-opacity:1" /></linearGradient></defs><circle cx="200" cy="150" r="80" fill="url(%23earth)"/><path d="M120 150 Q200 100 280 150" stroke="%2367c16d" stroke-width="3" fill="none"/><path d="M140 170 Q200 120 260 170" stroke="%2367c16d" stroke-width="2" fill="none"/></svg>') no-repeat center bottom;
  background-size: contain;
  opacity: 0.8;
}
/* Responsive design */
@media (min-width: 768px) {
  .app-container {
    max-width: var(--desktop-max-width);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px auto;
    min-height: calc(100vh - 40px);
  }

  .main-content {
    padding: 40px;
  }

  .app-title {
    font-size: 36px;
  }

  .mobile-only {
    display: none !important;
  }

  .verification-form {
    max-width: 450px;
  }

  .email-icon {
    width: 100px;
    height: 100px;
  }

  .otp-input {
    width: 70px;
    height: 70px;
    font-size: 28px;
  }
}

@media (max-width: 767px) {
  .mobile-only {
    display: flex;
  }

  .app-container {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .main-content {
    padding: 16px;
  }

  .otp-input {
    width: 55px;
    height: 55px;
    font-size: 20px;
  }

  .submit-button {
    font-size: 16px;
    min-height: 52px;
  }
}

/* Focus and accessibility improvements */
.otp-input:focus,
.submit-button:focus,
.resend-button:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* Animation for smooth transitions */
* {
  transition: border-color 0.2s ease, background-color 0.2s ease, color 0.2s ease;
}

/* OTP input animations */
.otp-input.shake {
  animation: shake 0.5s ease-in-out;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

/* Loading state for submit button */
.submit-button.loading {
  position: relative;
  color: transparent;
}

.submit-button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: 50%;
  left: 50%;
  margin-left: -10px;
  margin-top: -10px;
  border: 2px solid var(--dark-text);
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
