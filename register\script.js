// SakuHijau Registration Form JavaScript

// DOM elements
const registrationForm = document.getElementById('registrationForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const confirmPasswordInput = document.getElementById('confirmPassword');
const termsCheckbox = document.getElementById('terms');
const submitButton = document.querySelector('.submit-button');

// Tab switching functionality
function switchTab(tabName) {
    const loginTab = document.getElementById('login-tab');
    const registerTab = document.getElementById('register-tab');
    
    if (tabName === 'login') {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        // In a real app, you would switch to login form here
        alert('Login functionality would be implemented here');
    } else {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
    }
}

// Password visibility toggle
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.textContent = '🙈';
    } else {
        input.type = 'password';
        icon.textContent = '👁️';
    }
}

// Form validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password.length >= 6;
}

function validatePasswordMatch(password, confirmPassword) {
    return password === confirmPassword;
}

// Show error message
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message') || createErrorElement(formGroup);
    
    input.classList.add('error');
    errorElement.textContent = message;
    formGroup.classList.add('has-error');
}

// Clear error message
function clearError(input) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.classList.remove('error');
    formGroup.classList.remove('has-error');
    if (errorElement) {
        errorElement.textContent = '';
    }
}

// Create error element
function createErrorElement(formGroup) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    formGroup.appendChild(errorElement);
    return errorElement;
}

// Real-time validation
function setupRealTimeValidation() {
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !validateEmail(email)) {
            showError(this, 'Please enter a valid email address');
        } else {
            clearError(this);
        }
    });

    passwordInput.addEventListener('blur', function() {
        const password = this.value;
        if (password && !validatePassword(password)) {
            showError(this, 'Password must be at least 6 characters long');
        } else {
            clearError(this);
        }
        
        // Also validate confirm password if it has a value
        if (confirmPasswordInput.value) {
            validateConfirmPassword();
        }
    });

    confirmPasswordInput.addEventListener('blur', validateConfirmPassword);
}

function validateConfirmPassword() {
    const password = passwordInput.value;
    const confirmPassword = confirmPasswordInput.value;
    
    if (confirmPassword && !validatePasswordMatch(password, confirmPassword)) {
        showError(confirmPasswordInput, 'Passwords do not match');
    } else {
        clearError(confirmPasswordInput);
    }
}

// Form submission
function handleFormSubmission(event) {
    event.preventDefault();
    
    // Clear all previous errors
    [emailInput, passwordInput, confirmPasswordInput].forEach(clearError);
    
    let isValid = true;
    
    // Validate email
    const email = emailInput.value.trim();
    if (!email) {
        showError(emailInput, 'Email is required');
        isValid = false;
    } else if (!validateEmail(email)) {
        showError(emailInput, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate password
    const password = passwordInput.value;
    if (!password) {
        showError(passwordInput, 'Password is required');
        isValid = false;
    } else if (!validatePassword(password)) {
        showError(passwordInput, 'Password must be at least 6 characters long');
        isValid = false;
    }
    
    // Validate confirm password
    const confirmPassword = confirmPasswordInput.value;
    if (!confirmPassword) {
        showError(confirmPasswordInput, 'Please confirm your password');
        isValid = false;
    } else if (!validatePasswordMatch(password, confirmPassword)) {
        showError(confirmPasswordInput, 'Passwords do not match');
        isValid = false;
    }
    
    // Validate terms checkbox
    if (!termsCheckbox.checked) {
        alert('Please accept the terms and policies to continue');
        isValid = false;
    }
    
    if (isValid) {
        // Simulate form submission
        submitButton.textContent = 'Registering...';
        submitButton.disabled = true;
        
        setTimeout(() => {
            alert('Registration successful! Welcome to SakuHijau!');
            submitButton.textContent = 'Register';
            submitButton.disabled = false;
            registrationForm.reset();
        }, 2000);
    }
}

// Initialize the application
function initializeApp() {
    // Set up event listeners
    registrationForm.addEventListener('submit', handleFormSubmission);
    setupRealTimeValidation();
    
    // Focus on first input for better UX
    emailInput.focus();
    
    console.log('SakuHijau Registration Form initialized');
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateEmail,
        validatePassword,
        validatePasswordMatch,
        switchTab,
        togglePassword
    };
}
