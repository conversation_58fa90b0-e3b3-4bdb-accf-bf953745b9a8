// SakuHijau OTP Verification JavaScript

// DOM elements
const verificationForm = document.getElementById('verificationForm');
const otpInputs = document.querySelectorAll('.otp-input');
const emailAddress = document.getElementById('emailAddress');
const timerContainer = document.getElementById('timerContainer');
const timerCountdown = document.getElementById('timerCountdown');
const resendContainer = document.getElementById('resendContainer');
const resendBtn = document.getElementById('resendBtn');
const verifyBtn = document.getElementById('verifyBtn');
const errorMessage = document.getElementById('errorMessage');
const successMessage = document.getElementById('successMessage');

// State management
let currentOtpIndex = 0;
let timerSeconds = 60;
let timerInterval = null;
let isVerifying = false;
let correctOtp = '1234'; // In real app, this would come from server

// Initialize with user email (in real app, this would come from previous step)
const userEmail = '<EMAIL>';
emailAddress.textContent = userEmail;

// OTP input handling
function setupOtpInputs() {
    otpInputs.forEach((input, index) => {
        input.addEventListener('input', function(e) {
            const value = e.target.value;
            
            // Only allow numbers
            if (!/^\d$/.test(value) && value !== '') {
                e.target.value = '';
                return;
            }
            
            if (value) {
                input.classList.add('filled');
                
                // Move to next input
                if (index < otpInputs.length - 1) {
                    otpInputs[index + 1].focus();
                }
                
                // Check if all inputs are filled
                checkOtpComplete();
            } else {
                input.classList.remove('filled');
                updateVerifyButton();
            }
        });
        
        input.addEventListener('keydown', function(e) {
            // Handle backspace
            if (e.key === 'Backspace' && !input.value && index > 0) {
                otpInputs[index - 1].focus();
                otpInputs[index - 1].value = '';
                otpInputs[index - 1].classList.remove('filled');
                updateVerifyButton();
            }
            
            // Handle arrow keys
            if (e.key === 'ArrowLeft' && index > 0) {
                otpInputs[index - 1].focus();
            }
            if (e.key === 'ArrowRight' && index < otpInputs.length - 1) {
                otpInputs[index + 1].focus();
            }
            
            // Handle paste
            if (e.key === 'Enter') {
                e.preventDefault();
                if (isOtpComplete()) {
                    verifyOtp();
                }
            }
        });
        
        input.addEventListener('paste', function(e) {
            e.preventDefault();
            const pastedData = e.clipboardData.getData('text');
            const digits = pastedData.replace(/\D/g, '').slice(0, 4);
            
            if (digits.length === 4) {
                otpInputs.forEach((inp, idx) => {
                    inp.value = digits[idx] || '';
                    if (digits[idx]) {
                        inp.classList.add('filled');
                    }
                });
                checkOtpComplete();
            }
        });
        
        input.addEventListener('focus', function() {
            input.select();
        });
    });
}

// Check if OTP is complete
function isOtpComplete() {
    return Array.from(otpInputs).every(input => input.value.length === 1);
}

function checkOtpComplete() {
    updateVerifyButton();
    if (isOtpComplete()) {
        // Auto-verify after a short delay
        setTimeout(() => {
            if (isOtpComplete()) {
                verifyOtp();
            }
        }, 500);
    }
}

// Update verify button state
function updateVerifyButton() {
    verifyBtn.disabled = !isOtpComplete();
}

// Get current OTP value
function getCurrentOtp() {
    return Array.from(otpInputs).map(input => input.value).join('');
}

// Clear OTP inputs
function clearOtp() {
    otpInputs.forEach(input => {
        input.value = '';
        input.classList.remove('filled', 'error');
    });
    updateVerifyButton();
    otpInputs[0].focus();
}

// Show error
function showError(message) {
    errorMessage.textContent = message;
    errorMessage.style.display = 'block';
    successMessage.style.display = 'none';
    
    // Add shake animation to inputs
    otpInputs.forEach(input => {
        input.classList.add('error', 'shake');
        setTimeout(() => {
            input.classList.remove('shake');
        }, 500);
    });
    
    setTimeout(() => {
        errorMessage.style.display = 'none';
        otpInputs.forEach(input => input.classList.remove('error'));
    }, 3000);
}

// Show success
function showSuccess(message) {
    successMessage.textContent = message;
    successMessage.style.display = 'block';
    errorMessage.style.display = 'none';
}

// Verify OTP
function verifyOtp() {
    if (isVerifying) return;
    
    const otp = getCurrentOtp();
    if (otp.length !== 4) {
        showError('Please enter the complete 4-digit code');
        return;
    }
    
    isVerifying = true;
    verifyBtn.classList.add('loading');
    verifyBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        if (otp === correctOtp) {
            showSuccess('Code verified successfully!');
            setTimeout(() => {
                alert('Verification successful! Proceeding to next step...');
                console.log('OTP verified successfully');
            }, 1000);
        } else {
            showError('Invalid verification code. Please try again.');
            clearOtp();
        }
        
        isVerifying = false;
        verifyBtn.classList.remove('loading');
        updateVerifyButton();
    }, 2000);
}

// Timer functionality
function startTimer() {
    timerSeconds = 60;
    timerContainer.style.display = 'flex';
    resendContainer.style.display = 'none';
    
    timerInterval = setInterval(() => {
        timerSeconds--;
        timerCountdown.textContent = timerSeconds;
        
        if (timerSeconds <= 0) {
            clearInterval(timerInterval);
            timerContainer.style.display = 'none';
            resendContainer.style.display = 'flex';
        }
    }, 1000);
}

// Resend OTP
function resendOtp() {
    resendBtn.disabled = true;
    resendBtn.textContent = 'Sending...';
    
    // Simulate API call
    setTimeout(() => {
        // Generate new OTP (in real app, server would send this)
        correctOtp = Math.floor(1000 + Math.random() * 9000).toString();
        console.log('New OTP sent:', correctOtp); // For demo purposes
        
        clearOtp();
        startTimer();
        
        resendBtn.disabled = false;
        resendBtn.textContent = 'Resend it';
        
        alert('New verification code sent to your email!');
    }, 1500);
}

// Form submission
function handleFormSubmission(e) {
    e.preventDefault();
    verifyOtp();
}

// Event listeners
verificationForm.addEventListener('submit', handleFormSubmission);
resendBtn.addEventListener('click', resendOtp);

// Initialize the application
function initializeApp() {
    setupOtpInputs();
    startTimer();
    
    // Focus on first input
    otpInputs[0].focus();
    
    console.log('SakuHijau OTP Verification initialized');
    console.log('Demo OTP:', correctOtp); // For demo purposes
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        verifyOtp,
        isOtpComplete,
        getCurrentOtp
    };
}
