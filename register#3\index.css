/* CSS Variables for consistent theming */
:root {
  --primary-green: #67c16d;
  --light-green: #c7ebc9;
  --background-green: #f3faf3;
  --dark-text: #0d2610;
  --placeholder-text: #979797;
  --border-color: #e0e0e0;
  --white: #ffffff;
  --black: #000000;
  --error-color: #e74c3c;
  --success-color: #27ae60;

  --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Ubuntu, "Helvetica Neue", Helvetica, Arial, sans-serif;

  --border-radius: 8px;
  --border-radius-large: 16px;
  --shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.25);
  --shadow-hover: 0 6px 12px 0 rgba(0, 0, 0, 0.15);

  --mobile-max-width: 440px;
  --desktop-max-width: 500px;
}

/* Reset and base styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: var(--font-family);
  background-color: var(--white);
  color: var(--dark-text);
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

input,
select,
textarea,
button {
  outline: 0;
  font-family: inherit;
}

button {
  cursor: pointer;
  border: none;
  background: none;
}

/* Main container */
.app-container {
  min-height: 100vh;
  max-width: var(--mobile-max-width);
  margin: 0 auto;
  background: var(--white);
  position: relative;
  overflow-x: hidden;
}
/* Status bar (mobile only) */
.status-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  background: var(--white);
  height: 56px;
}

.mobile-only {
  display: none;
}

.status-time {
  font-size: 17px;
  font-weight: 600;
  color: var(--black);
  letter-spacing: -0.41px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.signal-icon,
.wifi-icon,
.battery-icon {
  width: 18px;
  height: 12px;
  background-color: var(--black);
  border-radius: 2px;
}

.battery-icon {
  width: 24px;
  height: 12px;
  border: 1px solid var(--black);
  background: linear-gradient(to right, var(--black) 70%, transparent 70%);
}

.wifi-icon {
  width: 16px;
  height: 12px;
  background: radial-gradient(circle at bottom, var(--black) 30%, transparent 30%);
}

/* Main content area */
.main-content {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32px;
  min-height: calc(100vh - 200px);
}

/* App header */
.app-header {
  text-align: center;
  margin-bottom: 16px;
}

.app-title {
  color: var(--primary-green);
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
  margin: 0;
}
/* Upload form */
.upload-form {
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.upload-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

/* Profile picture container */
.profile-picture-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  text-align: center;
}

.profile-picture-preview {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--background-green);
  border: 3px dashed var(--border-color);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.profile-picture-preview:hover {
  border-color: var(--primary-green);
  background: var(--light-green);
}

.profile-picture-preview.has-image {
  border-style: solid;
  border-color: var(--primary-green);
}

.placeholder-icon {
  color: var(--placeholder-text);
  transition: color 0.3s ease;
}

.profile-picture-preview:hover .placeholder-icon {
  color: var(--primary-green);
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.upload-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--dark-text);
  margin: 0;
}

.upload-subtitle {
  font-size: 14px;
  color: var(--placeholder-text);
  margin: 0;
  max-width: 280px;
}
/* Upload actions */
.upload-actions {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
  max-width: 343px;
}

.upload-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 400;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  width: 100%;
  border: none;
}

.upload-button.primary {
  background: var(--light-green);
  color: var(--dark-text);
}

.upload-button.primary:hover {
  background: var(--primary-green);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.upload-button.secondary {
  background: var(--background-green);
  color: var(--dark-text);
  border: 1px solid var(--border-color);
}

.upload-button.secondary:hover {
  background: var(--light-green);
  border-color: var(--primary-green);
  transform: translateY(-1px);
}

.upload-button.tertiary {
  background: transparent;
  color: var(--placeholder-text);
  border: 1px solid var(--border-color);
}

.upload-button.tertiary:hover {
  background: var(--background-green);
  color: var(--dark-text);
}

.button-icon {
  font-size: 18px;
}

.button-text {
  font-size: 16px;
}
/* Upload progress */
.upload-progress {
  width: 100%;
  max-width: 343px;
  text-align: center;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--primary-green);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  font-size: 14px;
  color: var(--placeholder-text);
}

/* Submit button */
.submit-button {
  width: 100%;
  max-width: 343px;
  padding: 12px 24px;
  background: var(--primary-green);
  color: var(--white);
  font-size: 18px;
  font-weight: 400;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.submit-button:hover {
  background: var(--dark-text);
  transform: translateY(-1px);
  box-shadow: var(--shadow-hover);
}

.submit-button:disabled {
  background: var(--border-color);
  color: var(--placeholder-text);
  cursor: not-allowed;
  transform: none;
}

/* Error messages */
.error-message {
  color: var(--error-color);
  font-size: 14px;
  text-align: center;
  padding: 12px;
  background: rgba(231, 76, 60, 0.1);
  border-radius: var(--border-radius);
  width: 100%;
  max-width: 343px;
}
/* Progress indicator */
.progress-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-top: 16px;
}

.progress-dot {
  width: 24px;
  height: 13px;
  background: var(--light-green);
  border-radius: 50px;
  transition: all 0.3s ease;
}

.progress-dot.active {
  width: 34px;
  background: var(--primary-green);
}

/* Bottom illustration */
.bottom-illustration {
  position: fixed;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  max-width: var(--mobile-max-width);
  height: 200px;
  pointer-events: none;
  z-index: -1;
}

.earth-hands {
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 200"><defs><linearGradient id="earth" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2367c16d;stop-opacity:1" /><stop offset="100%" style="stop-color:%234a9c50;stop-opacity:1" /></linearGradient></defs><circle cx="200" cy="150" r="80" fill="url(%23earth)"/><path d="M120 150 Q200 100 280 150" stroke="%2367c16d" stroke-width="3" fill="none"/><path d="M140 170 Q200 120 260 170" stroke="%2367c16d" stroke-width="2" fill="none"/></svg>') no-repeat center bottom;
  background-size: contain;
  opacity: 0.8;
}

/* Responsive design */
@media (min-width: 768px) {
  .app-container {
    max-width: var(--desktop-max-width);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    margin: 20px auto;
    min-height: calc(100vh - 40px);
  }

  .main-content {
    padding: 40px;
  }

  .app-title {
    font-size: 36px;
  }

  .mobile-only {
    display: none !important;
  }

  .upload-form {
    max-width: 450px;
  }

  .profile-picture-preview {
    width: 150px;
    height: 150px;
  }
}

@media (max-width: 767px) {
  .mobile-only {
    display: flex;
  }

  .app-container {
    border: none;
    border-radius: 0;
    box-shadow: none;
  }

  .main-content {
    padding: 16px;
  }

  .upload-button {
    font-size: 16px;
    min-height: 52px;
  }
}

/* Focus and accessibility improvements */
.upload-button:focus,
.submit-button:focus {
  outline: 2px solid var(--primary-green);
  outline-offset: 2px;
}

/* Animation for smooth transitions */
* {
  transition: border-color 0.2s ease, background-color 0.2s ease, color 0.2s ease;
}

/* Drag and drop styles */
.profile-picture-preview.drag-over {
  border-color: var(--primary-green);
  background: var(--light-green);
  transform: scale(1.05);
}
  z-index: 28;
}
