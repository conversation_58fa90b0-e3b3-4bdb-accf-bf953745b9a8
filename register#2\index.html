<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SakuHijau - Registrasi</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" />
    <link rel="stylesheet" href="index.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Mobile status bar (only visible on mobile) -->
      <div class="status-bar mobile-only">
        <div class="status-time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>

      <!-- Main content -->
      <div class="main-content">
        <header class="app-header">
          <h1 class="app-title">SakuHijau</h1>
        </header>

        <!-- Registration form -->
        <form class="registration-form" id="registrationForm" novalidate>
          <div class="form-group">
            <label for="firstName" class="form-label">First Name</label>
            <div class="input-container">
              <input
                type="text"
                id="firstName"
                name="firstName"
                class="form-input"
                placeholder="Your First Name"
                required
                autocomplete="given-name"
              />
            </div>
            <div class="error-message"></div>
          </div>

          <div class="form-group">
            <label for="lastName" class="form-label">Last Name</label>
            <div class="input-container">
              <input
                type="text"
                id="lastName"
                name="lastName"
                class="form-input"
                placeholder="Your Last Name"
                required
                autocomplete="family-name"
              />
            </div>
            <div class="error-message"></div>
          </div>

          <div class="form-group">
            <label for="phoneNumber" class="form-label">Phone Number</label>
            <div class="phone-input-container">
              <div class="country-selector">
                <div class="country-flag">🇮🇩</div>
                <span class="country-code">+62</span>
                <div class="dropdown-arrow">▼</div>
              </div>
              <div class="phone-separator"></div>
              <input
                type="tel"
                id="phoneNumber"
                name="phoneNumber"
                class="phone-input"
                placeholder="Enter Phone Number"
                required
                autocomplete="tel"
              />
            </div>
            <div class="error-message"></div>
          </div>

          <button type="submit" class="submit-button">Next</button>
        </form>

        <!-- Progress indicator -->
        <div class="progress-indicator">
          <div class="progress-dot"></div>
          <div class="progress-dot active"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
        </div>
      </div>

      <!-- Bottom illustration -->
      <div class="bottom-illustration">
        <div class="earth-hands"></div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
