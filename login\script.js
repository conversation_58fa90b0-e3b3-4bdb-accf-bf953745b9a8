// SakuHijau Login Form JavaScript

// DOM elements
const loginForm = document.getElementById('loginForm');
const emailInput = document.getElementById('email');
const passwordInput = document.getElementById('password');
const loginTab = document.getElementById('login-tab');
const registerTab = document.getElementById('register-tab');

// Form validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password.length >= 6;
}

// Show error message
function showError(input, message) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.classList.add('error');
    errorElement.textContent = message;
    formGroup.classList.add('has-error');
}

// Clear error message
function clearError(input) {
    const formGroup = input.closest('.form-group');
    const errorElement = formGroup.querySelector('.error-message');
    
    input.classList.remove('error');
    formGroup.classList.remove('has-error');
    if (errorElement) {
        errorElement.textContent = '';
    }
}

// Tab switching functionality
function switchTab(tabName) {
    if (tabName === 'login') {
        loginTab.classList.add('active');
        registerTab.classList.remove('active');
        // Already on login form
    } else {
        registerTab.classList.add('active');
        loginTab.classList.remove('active');
        // In a real app, you would navigate to registration form
        alert('Registration functionality would redirect to registration page');
    }
}

// Password visibility toggle
function togglePassword(inputId) {
    const input = document.getElementById(inputId);
    const icon = document.getElementById(inputId + '-icon');
    
    if (input.type === 'password') {
        input.type = 'text';
        icon.textContent = '🙈';
    } else {
        input.type = 'password';
        icon.textContent = '👁️';
    }
}

// Real-time validation
function setupRealTimeValidation() {
    emailInput.addEventListener('blur', function() {
        const email = this.value.trim();
        if (email && !validateEmail(email)) {
            showError(this, 'Please enter a valid email address');
        } else {
            clearError(this);
        }
    });

    passwordInput.addEventListener('blur', function() {
        const password = this.value;
        if (password && !validatePassword(password)) {
            showError(this, 'Password must be at least 6 characters long');
        } else {
            clearError(this);
        }
    });

    // Clear errors on input
    emailInput.addEventListener('input', function() {
        if (this.classList.contains('error')) {
            clearError(this);
        }
    });

    passwordInput.addEventListener('input', function() {
        if (this.classList.contains('error')) {
            clearError(this);
        }
    });
}

// Form submission
function handleFormSubmission(event) {
    event.preventDefault();
    
    // Clear all previous errors
    [emailInput, passwordInput].forEach(clearError);
    
    let isValid = true;
    
    // Validate email
    const email = emailInput.value.trim();
    if (!email) {
        showError(emailInput, 'Email is required');
        isValid = false;
    } else if (!validateEmail(email)) {
        showError(emailInput, 'Please enter a valid email address');
        isValid = false;
    }
    
    // Validate password
    const password = passwordInput.value;
    if (!password) {
        showError(passwordInput, 'Password is required');
        isValid = false;
    } else if (!validatePassword(password)) {
        showError(passwordInput, 'Password must be at least 6 characters long');
        isValid = false;
    }
    
    if (isValid) {
        // Show loading state
        const submitButton = loginForm.querySelector('.submit-button');
        submitButton.classList.add('loading');
        submitButton.disabled = true;
        
        // Simulate login API call
        setTimeout(() => {
            // In a real app, this would make an actual API call
            alert(`Login attempt:\nEmail: ${email}\nPassword: ${'*'.repeat(password.length)}\n\nLogin successful!`);
            
            // Reset loading state
            submitButton.classList.remove('loading');
            submitButton.disabled = false;
            
            console.log('Login successful:', { email });
        }, 2000);
    }
}

// Social login functions
function loginWithGoogle() {
    alert('Google login would be implemented here.\nThis would redirect to Google OAuth.');
    console.log('Google login initiated');
}

function loginWithFacebook() {
    alert('Facebook login would be implemented here.\nThis would redirect to Facebook OAuth.');
    console.log('Facebook login initiated');
}

function loginWithApple() {
    alert('Apple login would be implemented here.\nThis would redirect to Apple Sign In.');
    console.log('Apple login initiated');
}

// Input focus management for better UX
function setupInputFocusManagement() {
    // Auto-focus next input on Enter key
    emailInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            passwordInput.focus();
        }
    });
    
    passwordInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            loginForm.querySelector('.submit-button').click();
        }
    });
}

// Initialize the application
function initializeApp() {
    // Set up event listeners
    loginForm.addEventListener('submit', handleFormSubmission);
    setupRealTimeValidation();
    setupInputFocusManagement();
    
    // Focus on first input for better UX
    emailInput.focus();
    
    console.log('SakuHijau Login Form initialized');
}

// Initialize when DOM is loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeApp);
} else {
    initializeApp();
}

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        validateEmail,
        validatePassword,
        switchTab,
        togglePassword,
        loginWithGoogle,
        loginWithFacebook,
        loginWithApple
    };
}
