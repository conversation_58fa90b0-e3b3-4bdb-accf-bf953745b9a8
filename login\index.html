<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SakuHijau - Login</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" />
    <link rel="stylesheet" href="index.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Mobile status bar (only visible on mobile) -->
      <div class="status-bar mobile-only">
        <div class="status-time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>

      <!-- Main content -->
      <div class="main-content">
        <header class="app-header">
          <h1 class="app-title">SakuHijau</h1>
        </header>

        <!-- Tab navigation -->
        <div class="tab-container">
          <button class="tab-button active" id="login-tab" onclick="switchTab('login')">Masuk</button>
          <button class="tab-button" id="register-tab" onclick="switchTab('register')">Registrasi</button>
        </div>

        <!-- Login form -->
        <form class="login-form" id="loginForm" novalidate>
          <div class="form-group">
            <label for="email" class="form-label">Email</label>
            <div class="input-container">
              <input
                type="email"
                id="email"
                name="email"
                class="form-input"
                placeholder="Your email"
                required
                autocomplete="email"
              />
            </div>
            <div class="error-message"></div>
          </div>

          <div class="form-group">
            <label for="password" class="form-label">Password</label>
            <div class="input-container">
              <input
                type="password"
                id="password"
                name="password"
                class="form-input"
                placeholder="Password"
                required
                minlength="6"
                autocomplete="current-password"
              />
              <button type="button" class="password-toggle" onclick="togglePassword('password')">
                <span class="password-icon" id="password-icon">👁️</span>
              </button>
            </div>
            <div class="error-message"></div>
          </div>

          <button type="submit" class="submit-button">Login</button>
        </form>

        <!-- Social login options -->
        <div class="social-login">
          <p class="social-title">Other sign in options</p>
          <div class="social-buttons">
            <button type="button" class="social-button google" onclick="loginWithGoogle()">
              <svg class="social-icon" viewBox="0 0 24 24">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
              </svg>
              <span>Google</span>
            </button>

            <button type="button" class="social-button facebook" onclick="loginWithFacebook()">
              <svg class="social-icon" viewBox="0 0 24 24">
                <path fill="#1877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              <span>Facebook</span>
            </button>

            <button type="button" class="social-button apple" onclick="loginWithApple()">
              <svg class="social-icon" viewBox="0 0 24 24">
                <path fill="#000000" d="M12.152 6.896c-.948 0-2.415-1.078-3.96-1.04-2.04.027-3.91 1.183-4.961 3.014-2.117 3.675-.546 9.103 1.519 12.09 1.013 1.454 2.208 3.09 3.792 3.039 1.52-.065 2.09-.987 3.935-.987 1.831 0 2.35.987 3.96.948 1.637-.026 2.676-1.48 3.676-2.948 1.156-1.688 1.636-3.325 1.662-3.415-.039-.013-3.182-1.221-3.22-4.857-.026-3.04 2.48-4.494 2.597-4.559-1.429-2.09-3.623-2.324-4.39-2.376-2-.156-3.675 1.09-4.61 1.09zM15.53 3.83c.843-1.012 1.4-2.427 1.245-3.83-1.207.052-2.662.805-3.532 1.818-.78.896-1.454 2.338-1.273 3.714 1.338.104 2.715-.688 3.559-1.701"/>
              </svg>
              <span>Apple</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Bottom illustration -->
      <div class="bottom-illustration">
        <div class="earth-hands"></div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
