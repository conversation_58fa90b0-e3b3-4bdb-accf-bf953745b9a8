<!DOCTYPE html>
<html lang="id">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SakuHijau - Upload Foto Profil</title>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" />
    <link rel="stylesheet" href="index.css" />
  </head>
  <body>
    <div class="app-container">
      <!-- Mobile status bar (only visible on mobile) -->
      <div class="status-bar mobile-only">
        <div class="status-time">9:41</div>
        <div class="status-icons">
          <div class="signal-icon"></div>
          <div class="wifi-icon"></div>
          <div class="battery-icon"></div>
        </div>
      </div>

      <!-- Main content -->
      <div class="main-content">
        <header class="app-header">
          <h1 class="app-title">SakuHijau</h1>
        </header>

        <!-- Profile picture upload form -->
        <form class="upload-form" id="uploadForm" novalidate>
          <div class="upload-section">
            <div class="profile-picture-container">
              <div class="profile-picture-preview" id="profilePreview">
                <div class="placeholder-icon">
                  <svg width="60" height="60" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="8" r="3" stroke="currentColor" stroke-width="2"/>
                    <path d="M12 14c-4 0-7 2-7 4v2h14v-2c0-2-3-4-7-4z" stroke="currentColor" stroke-width="2"/>
                  </svg>
                </div>
                <img id="previewImage" class="preview-image" alt="Profile preview" style="display: none;" />
              </div>
              <h2 class="upload-title">Your Profile Picture</h2>
              <p class="upload-subtitle">Add a photo to personalize your account</p>
            </div>

            <div class="upload-actions">
              <!-- Hidden file input -->
              <input
                type="file"
                id="fileInput"
                accept="image/*"
                style="display: none;"
              />

              <!-- Hidden camera input -->
              <input
                type="file"
                id="cameraInput"
                accept="image/*"
                capture="user"
                style="display: none;"
              />

              <button type="button" class="upload-button primary" id="chooseFileBtn">
                <span class="button-icon">📁</span>
                <span class="button-text">Choose Existing Picture</span>
              </button>

              <button type="button" class="upload-button secondary" id="takePictureBtn">
                <span class="button-icon">📷</span>
                <span class="button-text">Take a Picture</span>
              </button>

              <button type="button" class="upload-button tertiary" id="skipBtn">
                <span class="button-text">Skip</span>
              </button>
            </div>

            <!-- Upload progress -->
            <div class="upload-progress" id="uploadProgress" style="display: none;">
              <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
              </div>
              <span class="progress-text" id="progressText">Uploading...</span>
            </div>

            <!-- Error message -->
            <div class="error-message" id="errorMessage" style="display: none;"></div>
          </div>

          <button type="submit" class="submit-button" id="continueBtn" style="display: none;">
            Continue
          </button>
        </form>

        <!-- Progress indicator -->
        <div class="progress-indicator">
          <div class="progress-dot"></div>
          <div class="progress-dot"></div>
          <div class="progress-dot active"></div>
          <div class="progress-dot"></div>
        </div>
      </div>

      <!-- Bottom illustration -->
      <div class="bottom-illustration">
        <div class="earth-hands"></div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
